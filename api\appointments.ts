import { AppointmentDatabaseService } from '@/services/AppointmentDatabaseService';

export default async (req, res) => {
  if (req.method === 'POST') {
    const data = await req.json();
    const appt = await AppointmentDatabaseService.createAppointment(data);
    return new Response(JSON.stringify(appt), { headers: { 'Content-Type': 'application/json' } });
  }
  // GET all
  const list = await AppointmentDatabaseService.getAllAppointments();
  return new Response(JSON.stringify(list), { headers: { 'Content-Type': 'application/json' } });
};
